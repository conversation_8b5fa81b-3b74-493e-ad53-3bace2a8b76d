server:
  port: 8081
spring:
  profiles:
    active: dev
  application:
    name: bullish-bot

  datasource:
    url: ********************************/${POSTGRES_DB}
    username: ${POSTGRES_USER}
    password: ${POSTGRES_PASSWORD}
    hikari:
      max-lifetime: 60000
      auto-commit: false
  data:
    redis:
      host: ${SPRING_DATA_REDIS_HOST:redis}
      port: ${SPRING_DATA_REDIS_PORT:6379}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.xml
  thymeleaf:
    mode: HTML
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
springdoc:
  swagger-ui:
    path: /api/index.html
logging:
  pattern:
    correlation: "${spring.application.name:},%X{traceId:-},%X{spanId:-}"

# Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      client-id: bullish-bot-producer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 5
      buffer-memory: 33554432
      compression-type: snappy
      max-in-flight-requests-per-connection: 5
      enable-idempotence: true
      request-timeout-ms: 30000
      delivery-timeout-ms: 120000
    consumer:
      group-id: bullish-bot-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
      max-poll-interval-ms: 300000
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000
      fetch-min-size: 1
      fetch-max-wait: 500
    topics:
      replication-factor: ${KAFKA_REPLICATION_FACTOR:1}