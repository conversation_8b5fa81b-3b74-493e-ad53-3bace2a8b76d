package com.phdhuy.bullishbot.infrastructure.kafka.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.serializer.JsonDeserializer;

/**
 * Kafka Consumer Configuration for Bullish Bot.
 * 
 * <p>Configures Kafka consumers with proper deserialization, error handling, and performance
 * optimizations following Spring Boot best practices and SonarLint compliance.
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class KafkaConsumerConfig {

  @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
  private String bootstrapServers;

  @Value("${spring.kafka.consumer.group-id:bullish-bot-consumer-group}")
  private String groupId;

  @Value("${spring.kafka.consumer.auto-offset-reset:earliest}")
  private String autoOffsetReset;

  @Value("${spring.kafka.consumer.enable-auto-commit:false}")
  private Boolean enableAutoCommit;

  @Value("${spring.kafka.consumer.max-poll-records:500}")
  private Integer maxPollRecords;

  @Value("${spring.kafka.consumer.max-poll-interval-ms:300000}")
  private Integer maxPollIntervalMs;

  @Value("${spring.kafka.consumer.session-timeout-ms:30000}")
  private Integer sessionTimeoutMs;

  @Value("${spring.kafka.consumer.heartbeat-interval-ms:10000}")
  private Integer heartbeatIntervalMs;

  @Value("${spring.kafka.consumer.fetch-min-size:1}")
  private Integer fetchMinSize;

  @Value("${spring.kafka.consumer.fetch-max-wait:500}")
  private Integer fetchMaxWait;

  private final ObjectMapper kafkaObjectMapper;

  /**
   * Creates the consumer factory with optimized configuration for reliability and performance.
   *
   * @return configured ConsumerFactory
   */
  @Bean
  public ConsumerFactory<String, Object> consumerFactory() {
    Map<String, Object> configProps = createConsumerProperties();
    
    DefaultKafkaConsumerFactory<String, Object> factory = 
        new DefaultKafkaConsumerFactory<>(configProps);
    
    // Configure custom JSON deserializer with proper ObjectMapper
    factory.setValueDeserializer(createJsonDeserializer());
    
    log.info("Kafka consumer factory configured with bootstrap servers: {}", bootstrapServers);
    return factory;
  }

  /**
   * Creates Kafka listener container factory with proper error handling and concurrency.
   *
   * @return configured ConcurrentKafkaListenerContainerFactory
   */
  @Bean
  public ConcurrentKafkaListenerContainerFactory<String, Object> kafkaListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, Object> factory = 
        new ConcurrentKafkaListenerContainerFactory<>();
    
    factory.setConsumerFactory(consumerFactory());
    
    // Configure concurrency (number of consumer threads per topic)
    factory.setConcurrency(3);
    
    // Configure manual acknowledgment mode for better control
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
    
    // Configure error handling
    factory.setCommonErrorHandler(new KafkaErrorHandler());
    
    // Configure batch processing if needed
    // factory.setBatchListener(true);
    
    log.info("Kafka listener container factory configured successfully");
    return factory;
  }

  /**
   * Creates consumer configuration properties.
   *
   * @return Map of consumer configuration properties
   */
  private Map<String, Object> createConsumerProperties() {
    Map<String, Object> configProps = new HashMap<>();
    
    // Basic configuration
    configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
    configProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
    configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
    
    // Offset management
    configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);
    configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
    
    // Performance configuration
    configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
    configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, maxPollIntervalMs);
    configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeoutMs);
    configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, heartbeatIntervalMs);
    configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, fetchMinSize);
    configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, fetchMaxWait);
    
    // JSON deserializer configuration
    configProps.put(JsonDeserializer.TRUSTED_PACKAGES, "com.phdhuy.bullishbot.domain.event");
    configProps.put(JsonDeserializer.USE_TYPE_INFO_HEADERS, false);
    configProps.put(JsonDeserializer.VALUE_DEFAULT_TYPE, Object.class);
    
    return configProps;
  }

  /**
   * Creates JSON deserializer with custom ObjectMapper.
   *
   * @return configured JsonDeserializer
   */
  private JsonDeserializer<Object> createJsonDeserializer() {
    JsonDeserializer<Object> deserializer = new JsonDeserializer<>(kafkaObjectMapper);
    deserializer.addTrustedPackages("com.phdhuy.bullishbot.domain.event");
    deserializer.setUseTypeHeaders(false);
    return deserializer;
  }
}
