package com.phdhuy.bullishbot.infrastructure.kafka.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import com.phdhuy.bullishbot.domain.event.NotificationFailedEvent;
import com.phdhuy.bullishbot.domain.port.out.NewsNotificationRepository;
import com.phdhuy.bullishbot.infrastructure.kafka.config.KafkaTopicConfig;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Kafka consumer for NotificationFailedEvent messages (Dead Letter Queue).
 * 
 * <p>This consumer listens to the notifications.failed.dlq topic and processes
 * failed notification events for monitoring, alerting, and potential retry scenarios.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NotificationFailedConsumer {

  private final NewsNotificationRepository newsNotificationRepository;
  private final ObjectMapper kafkaObjectMapper;

  private static final int MAX_RETRY_COUNT = 3;

  /**
   * Consumes NotificationFailedEvent messages from the dead letter queue topic.
   *
   * @param record the Kafka consumer record
   * @param payload the event payload as raw string
   * @param partition the partition number
   * @param offset the message offset
   * @param acknowledgment the acknowledgment for manual commit
   */
  @KafkaListener(
      topics = KafkaTopicConfig.NOTIFICATIONS_FAILED_DLQ_TOPIC,
      groupId = "bullish-bot-notification-dlq-processor",
      containerFactory = "kafkaListenerContainerFactory"
  )
  public void consume(
      ConsumerRecord<String, Object> record,
      @Payload String payload,
      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
      @Header(KafkaHeaders.OFFSET) long offset,
      Acknowledgment acknowledgment) {

    log.warn("Received NotificationFailedEvent from DLQ at partition {} offset {}: {}", 
        partition, offset, payload);

    try {
      // Deserialize the event
      NotificationFailedEvent event = deserializeEvent(payload);
      
      if (event == null) {
        log.error("Failed to deserialize NotificationFailedEvent from DLQ, skipping message at offset {}", offset);
        acknowledgment.acknowledge();
        return;
      }

      // Process the failed notification event
      processFailedNotification(event);

      // Acknowledge processing
      acknowledgment.acknowledge();
      
      log.info("Successfully processed NotificationFailedEvent from DLQ for user: {} and article: {}", 
          event.getTelegramChatId(), event.getArticleTitle());

    } catch (Exception e) {
      log.error("Error processing NotificationFailedEvent from DLQ at partition {} offset {}: {}", 
          partition, offset, e.getMessage(), e);
      
      // For DLQ processing, we typically acknowledge even on error to prevent infinite loops
      acknowledgment.acknowledge();
    }
  }

  /**
   * Processes a failed notification event for monitoring and potential recovery.
   *
   * @param event the NotificationFailedEvent to process
   */
  private void processFailedNotification(NotificationFailedEvent event) {
    log.error("Processing failed notification - User: {}, Article: {}, Error: {}, Retry Count: {}, Recoverable: {}", 
        event.getTelegramChatId(), 
        event.getArticleTitle(), 
        event.getErrorMessage(),
        event.getRetryCount(),
        event.getRecoverable());

    // Update the notification record in the database if it exists
    Optional<NewsNotification> notificationOpt = 
        newsNotificationRepository.findById(event.getNotificationId());
    
    if (notificationOpt.isPresent()) {
      NewsNotification notification = notificationOpt.get();
      
      // Update the notification with failure details
      notification.setStatus(NotificationStatus.FAILED);
      notification.setErrorMessage(event.getErrorMessage());
      notification.setRetryCount(event.getRetryCount());
      
      newsNotificationRepository.save(notification);
      
      log.info("Updated notification {} status to FAILED with retry count {}", 
          notification.getId(), event.getRetryCount());
    } else {
      log.warn("Notification record {} not found in database for failed event", 
          event.getNotificationId());
    }

    // Log metrics for monitoring
    logFailureMetrics(event);

    // In a production environment, you might want to:
    // 1. Send alerts to monitoring systems
    // 2. Store failure details in a separate failure tracking table
    // 3. Implement automatic retry logic for recoverable errors
    // 4. Generate reports for manual investigation
    
    if (shouldRetry(event)) {
      log.info("Notification failure is recoverable and under retry limit, considering retry for notification {}", 
          event.getNotificationId());
      // TODO: Implement retry logic here
    } else {
      log.warn("Notification failure is not recoverable or exceeded retry limit, marking as permanently failed: {}", 
          event.getNotificationId());
      // TODO: Implement permanent failure handling here
    }
  }

  /**
   * Determines if a failed notification should be retried.
   *
   * @param event the NotificationFailedEvent
   * @return true if should retry, false otherwise
   */
  private boolean shouldRetry(NotificationFailedEvent event) {
    return event.getRecoverable() != null && 
           event.getRecoverable() && 
           event.getRetryCount() != null && 
           event.getRetryCount() < MAX_RETRY_COUNT;
  }

  /**
   * Logs failure metrics for monitoring and alerting.
   *
   * @param event the NotificationFailedEvent
   */
  private void logFailureMetrics(NotificationFailedEvent event) {
    // In a production environment, you would send these metrics to your monitoring system
    // (e.g., Micrometer, Prometheus, CloudWatch, etc.)
    
    log.info("METRIC: notification_failure_count=1 error_type={} recoverable={} retry_count={}", 
        event.getErrorType(), event.getRecoverable(), event.getRetryCount());
    
    if (event.getRetryCount() != null && event.getRetryCount() >= MAX_RETRY_COUNT) {
      log.error("ALERT: notification_permanent_failure user={} article={} error={}", 
          event.getTelegramChatId(), event.getArticleId(), event.getErrorMessage());
    }
  }

  /**
   * Deserializes the event payload into a NotificationFailedEvent object.
   *
   * @param payload the JSON payload
   * @return the deserialized event, or null if deserialization fails
   */
  private NotificationFailedEvent deserializeEvent(String payload) {
    try {
      return kafkaObjectMapper.readValue(payload, NotificationFailedEvent.class);
    } catch (Exception e) {
      log.error("Failed to deserialize NotificationFailedEvent payload: {}", payload, e);
      return null;
    }
  }
}
