package com.phdhuy.bullishbot.infrastructure.kafka.service;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.event.ArticleCrawledEvent;
import com.phdhuy.bullishbot.domain.event.NotificationFailedEvent;
import com.phdhuy.bullishbot.domain.event.NotificationRequestedEvent;
import com.phdhuy.bullishbot.domain.event.NotificationSentEvent;
import com.phdhuy.bullishbot.domain.event.TickersExtractedEvent;
import com.phdhuy.bullishbot.domain.port.out.EventPublisher;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for publishing domain events related to the news processing workflow.
 * 
 * <p>This service provides convenient methods for creating and publishing domain events
 * from domain entities, abstracting the event creation logic from the application services.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EventPublishingService {

  private final EventPublisher eventPublisher;

  /**
   * Publishes an ArticleCrawledEvent when a new article is successfully crawled.
   *
   * @param newsArticle the crawled news article
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishArticleCrawledEvent(NewsArticle newsArticle) {
    if (newsArticle == null) {
      throw new IllegalArgumentException("NewsArticle cannot be null");
    }

    ArticleCrawledEvent event = ArticleCrawledEvent.of(
        newsArticle.getId(),
        newsArticle.getTitle(),
        newsArticle.getUrl(),
        newsArticle.getDescription(),
        newsArticle.getContent(),
        newsArticle.getAuthor(),
        newsArticle.getPublishedDate(),
        newsArticle.getRssFeed().getId(),
        newsArticle.getRssFeed().getName(),
        newsArticle.getRssFeed().getUrl()
    );

    log.debug("Publishing ArticleCrawledEvent for article: {}", newsArticle.getTitle());
    return eventPublisher.publishEvent(event);
  }

  /**
   * Publishes a TickersExtractedEvent when ticker symbols are extracted from an article.
   *
   * @param newsArticle the news article with extracted tickers
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishTickersExtractedEvent(NewsArticle newsArticle) {
    if (newsArticle == null) {
      throw new IllegalArgumentException("NewsArticle cannot be null");
    }

    if (newsArticle.getExtractedTickers() == null || newsArticle.getExtractedTickers().isEmpty()) {
      log.debug("No tickers extracted for article: {}, skipping event publication", newsArticle.getTitle());
      return CompletableFuture.completedFuture(null);
    }

    TickersExtractedEvent event = TickersExtractedEvent.of(
        newsArticle.getId(),
        newsArticle.getTitle(),
        newsArticle.getUrl(),
        newsArticle.getDescription(),
        newsArticle.getPublishedDate(),
        newsArticle.getExtractedTickers(),
        newsArticle.getRssFeed().getId(),
        newsArticle.getRssFeed().getName()
    );

    log.debug("Publishing TickersExtractedEvent for article: {} with tickers: {}", 
        newsArticle.getTitle(), newsArticle.getExtractedTickers());
    return eventPublisher.publishEvent(event);
  }

  /**
   * Publishes a NotificationRequestedEvent when a notification needs to be sent to a user.
   *
   * @param user the user to notify
   * @param newsArticle the news article to notify about
   * @param matchedTickers the ticker symbols that matched the user's subscriptions
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishNotificationRequestedEvent(
      User user, NewsArticle newsArticle, Set<String> matchedTickers) {

    return publishNotificationRequestedEvent(null, user, newsArticle, matchedTickers);
  }

  /**
   * Publishes a NotificationRequestedEvent with a specific notification ID.
   *
   * @param notificationId the notification ID (can be null for new notifications)
   * @param user the user to notify
   * @param newsArticle the news article to notify about
   * @param matchedTickers the ticker symbols that matched the user's subscriptions
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishNotificationRequestedEvent(
      UUID notificationId, User user, NewsArticle newsArticle, Set<String> matchedTickers) {
    
    if (user == null) {
      throw new IllegalArgumentException("User cannot be null");
    }
    if (newsArticle == null) {
      throw new IllegalArgumentException("NewsArticle cannot be null");
    }
    if (matchedTickers == null || matchedTickers.isEmpty()) {
      throw new IllegalArgumentException("MatchedTickers cannot be null or empty");
    }

    NotificationRequestedEvent event = NotificationRequestedEvent.of(
        notificationId != null ? notificationId : UUID.randomUUID(), // Use provided ID or generate new one
        user.getId(),
        user.getTelegramChatId(),
        user.getUsername(),
        newsArticle.getId(),
        newsArticle.getTitle(),
        newsArticle.getUrl(),
        newsArticle.getDescription(),
        newsArticle.getPublishedDate(),
        matchedTickers,
        newsArticle.getRssFeed().getName()
    );

    log.debug("Publishing NotificationRequestedEvent for user: {} and article: {}", 
        user.getTelegramChatId(), newsArticle.getTitle());
    return eventPublisher.publishEvent(event);
  }

  /**
   * Publishes a NotificationSentEvent when a notification is successfully sent.
   *
   * @param notification the news notification that was sent
   * @param matchedTickers the ticker symbols mentioned in the notification
   * @param deliveryTimeMs time taken to deliver the notification
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishNotificationSentEvent(
      NewsNotification notification, Set<String> matchedTickers, Long deliveryTimeMs) {
    
    if (notification == null) {
      throw new IllegalArgumentException("NewsNotification cannot be null");
    }

    NotificationSentEvent event = NotificationSentEvent.of(
        notification.getId(),
        notification.getUser().getId(),
        notification.getUser().getTelegramChatId(),
        notification.getNewsArticle().getId(),
        notification.getNewsArticle().getTitle(),
        matchedTickers,
        notification.getSentAt() != null ? notification.getSentAt() : Instant.now(),
        deliveryTimeMs
    );

    log.debug("Publishing NotificationSentEvent for notification: {}", notification.getId());
    return eventPublisher.publishEvent(event);
  }

  /**
   * Publishes a NotificationFailedEvent when a notification fails to be sent.
   *
   * @param notification the news notification that failed
   * @param matchedTickers the ticker symbols that should have been mentioned
   * @param exception the exception that caused the failure
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishNotificationFailedEvent(
      NewsNotification notification, Set<String> matchedTickers, Exception exception) {
    
    if (notification == null) {
      throw new IllegalArgumentException("NewsNotification cannot be null");
    }
    if (exception == null) {
      throw new IllegalArgumentException("Exception cannot be null");
    }

    NotificationFailedEvent event = NotificationFailedEvent.fromException(
        notification.getId(),
        notification.getUser().getId(),
        notification.getUser().getTelegramChatId(),
        notification.getNewsArticle().getId(),
        notification.getNewsArticle().getTitle(),
        matchedTickers,
        exception,
        notification.getRetryCount()
    );

    log.debug("Publishing NotificationFailedEvent for notification: {} with error: {}", 
        notification.getId(), exception.getMessage());
    return eventPublisher.publishEvent(event);
  }

  /**
   * Publishes a NotificationFailedEvent with custom error details.
   *
   * @param notificationId unique identifier of the notification
   * @param userId unique identifier of the user
   * @param telegramChatId Telegram chat ID of the user
   * @param articleId unique identifier of the article
   * @param articleTitle title of the article
   * @param matchedTickers ticker symbols that should have been mentioned
   * @param errorMessage description of the error
   * @param errorType type/category of the error
   * @param retryCount number of retry attempts made
   * @param recoverable whether the error is recoverable
   * @return CompletableFuture that completes when the event is published
   */
  public CompletableFuture<Void> publishNotificationFailedEvent(
      UUID notificationId, UUID userId, Long telegramChatId, UUID articleId, String articleTitle,
      Set<String> matchedTickers, String errorMessage, String errorType, 
      Integer retryCount, Boolean recoverable) {

    NotificationFailedEvent event = NotificationFailedEvent.of(
        notificationId, userId, telegramChatId, articleId, articleTitle,
        matchedTickers, Instant.now(), errorMessage, errorType, retryCount, recoverable
    );

    log.debug("Publishing NotificationFailedEvent for notification: {} with error: {}", 
        notificationId, errorMessage);
    return eventPublisher.publishEvent(event);
  }
}
