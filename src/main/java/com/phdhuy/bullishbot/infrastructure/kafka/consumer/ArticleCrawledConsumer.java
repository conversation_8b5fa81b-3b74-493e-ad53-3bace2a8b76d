package com.phdhuy.bullishbot.infrastructure.kafka.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phdhuy.bullishbot.application.handler.ArticleCrawledEventHandler;
import com.phdhuy.bullishbot.domain.event.ArticleCrawledEvent;
import com.phdhuy.bullishbot.infrastructure.kafka.config.KafkaTopicConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Kafka consumer for ArticleCrawledEvent messages.
 * 
 * <p>This consumer listens to the articles.crawled topic and processes newly crawled
 * articles by extracting ticker symbols from their content.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ArticleCrawledConsumer {

  private final ArticleCrawledEventHandler eventHandler;
  private final ObjectMapper kafkaObjectMapper;

  /**
   * Consumes ArticleCrawledEvent messages from the Kafka topic.
   *
   * @param record the Kafka consumer record
   * @param payload the event payload as raw string
   * @param partition the partition number
   * @param offset the message offset
   * @param acknowledgment the acknowledgment for manual commit
   */
  @KafkaListener(
      topics = KafkaTopicConfig.ARTICLES_CRAWLED_TOPIC,
      groupId = "bullish-bot-article-processor",
      containerFactory = "kafkaListenerContainerFactory"
  )
  public void consume(
      ConsumerRecord<String, Object> record,
      @Payload String payload,
      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
      @Header(KafkaHeaders.OFFSET) long offset,
      Acknowledgment acknowledgment) {

    log.debug("Received ArticleCrawledEvent from partition {} at offset {}: {}", 
        partition, offset, payload);

    try {
      // Deserialize the event
      ArticleCrawledEvent event = deserializeEvent(payload);
      
      if (event == null) {
        log.warn("Failed to deserialize ArticleCrawledEvent, skipping message at offset {}", offset);
        acknowledgment.acknowledge();
        return;
      }

      // Process the event
      eventHandler.handle(event);

      // Acknowledge successful processing
      acknowledgment.acknowledge();
      
      log.debug("Successfully processed ArticleCrawledEvent for article: {} (ID: {})", 
          event.getTitle(), event.getArticleId());

    } catch (Exception e) {
      log.error("Error processing ArticleCrawledEvent at partition {} offset {}: {}", 
          partition, offset, e.getMessage(), e);
      
      // Don't acknowledge - this will trigger retry or dead letter queue handling
      throw new RuntimeException("Failed to process ArticleCrawledEvent", e);
    }
  }

  /**
   * Deserializes the event payload into an ArticleCrawledEvent object.
   *
   * @param payload the JSON payload
   * @return the deserialized event, or null if deserialization fails
   */
  private ArticleCrawledEvent deserializeEvent(String payload) {
    try {
      return kafkaObjectMapper.readValue(payload, ArticleCrawledEvent.class);
    } catch (Exception e) {
      log.error("Failed to deserialize ArticleCrawledEvent payload: {}", payload, e);
      return null;
    }
  }
}
