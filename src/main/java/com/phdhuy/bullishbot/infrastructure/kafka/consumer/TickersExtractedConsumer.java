package com.phdhuy.bullishbot.infrastructure.kafka.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phdhuy.bullishbot.application.handler.TickersExtractedEventHandler;
import com.phdhuy.bullishbot.domain.event.TickersExtractedEvent;
import com.phdhuy.bullishbot.infrastructure.kafka.config.KafkaTopicConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Kafka consumer for TickersExtractedEvent messages.
 * 
 * <p>This consumer listens to the tickers.extracted topic and processes articles
 * with extracted ticker symbols by finding subscribed users and triggering notifications.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TickersExtractedConsumer {

  private final TickersExtractedEventHandler eventHandler;
  private final ObjectMapper kafkaObjectMapper;

  /**
   * Consumes TickersExtractedEvent messages from the Kafka topic.
   *
   * @param record the Kafka consumer record
   * @param payload the event payload as raw string
   * @param partition the partition number
   * @param offset the message offset
   * @param acknowledgment the acknowledgment for manual commit
   */
  @KafkaListener(
      topics = KafkaTopicConfig.TICKERS_EXTRACTED_TOPIC,
      groupId = "bullish-bot-notification-processor",
      containerFactory = "kafkaListenerContainerFactory"
  )
  public void consume(
      ConsumerRecord<String, Object> record,
      @Payload String payload,
      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
      @Header(KafkaHeaders.OFFSET) long offset,
      Acknowledgment acknowledgment) {

    log.debug("Received TickersExtractedEvent from partition {} at offset {}: {}", 
        partition, offset, payload);

    try {
      // Deserialize the event
      TickersExtractedEvent event = deserializeEvent(payload);
      
      if (event == null) {
        log.warn("Failed to deserialize TickersExtractedEvent, skipping message at offset {}", offset);
        acknowledgment.acknowledge();
        return;
      }

      // Process the event
      eventHandler.handle(event);

      // Acknowledge successful processing
      acknowledgment.acknowledge();
      
      log.debug("Successfully processed TickersExtractedEvent for article: {} (ID: {}) with {} tickers", 
          event.getTitle(), event.getArticleId(), event.getTickerCount());

    } catch (Exception e) {
      log.error("Error processing TickersExtractedEvent at partition {} offset {}: {}", 
          partition, offset, e.getMessage(), e);
      
      // Don't acknowledge - this will trigger retry or dead letter queue handling
      throw new RuntimeException("Failed to process TickersExtractedEvent", e);
    }
  }

  /**
   * Deserializes the event payload into a TickersExtractedEvent object.
   *
   * @param payload the JSON payload
   * @return the deserialized event, or null if deserialization fails
   */
  private TickersExtractedEvent deserializeEvent(String payload) {
    try {
      return kafkaObjectMapper.readValue(payload, TickersExtractedEvent.class);
    } catch (Exception e) {
      log.error("Failed to deserialize TickersExtractedEvent payload: {}", payload, e);
      return null;
    }
  }
}
