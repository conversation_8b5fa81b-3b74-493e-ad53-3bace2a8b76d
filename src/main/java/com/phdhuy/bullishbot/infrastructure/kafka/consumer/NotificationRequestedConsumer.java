package com.phdhuy.bullishbot.infrastructure.kafka.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phdhuy.bullishbot.application.handler.NotificationRequestedEventHandler;
import com.phdhuy.bullishbot.domain.event.NotificationRequestedEvent;
import com.phdhuy.bullishbot.infrastructure.kafka.config.KafkaTopicConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Kafka consumer for NotificationRequestedEvent messages.
 * 
 * <p>This consumer listens to the notifications.requested topic and processes
 * notification requests by sending the actual notifications to users via Telegram.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NotificationRequestedConsumer {

  private final NotificationRequestedEventHandler eventHandler;
  private final ObjectMapper kafkaObjectMapper;

  /**
   * Consumes NotificationRequestedEvent messages from the Kafka topic.
   *
   * @param record the Kafka consumer record
   * @param payload the event payload as raw string
   * @param partition the partition number
   * @param offset the message offset
   * @param acknowledgment the acknowledgment for manual commit
   */
  @KafkaListener(
      topics = KafkaTopicConfig.NOTIFICATIONS_REQUESTED_TOPIC,
      groupId = "bullish-bot-notification-sender",
      containerFactory = "kafkaListenerContainerFactory"
  )
  public void consume(
      ConsumerRecord<String, Object> record,
      @Payload String payload,
      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
      @Header(KafkaHeaders.OFFSET) long offset,
      Acknowledgment acknowledgment) {

    log.debug("Received NotificationRequestedEvent from partition {} at offset {}: {}", 
        partition, offset, payload);

    try {
      // Deserialize the event
      NotificationRequestedEvent event = deserializeEvent(payload);
      
      if (event == null) {
        log.warn("Failed to deserialize NotificationRequestedEvent, skipping message at offset {}", offset);
        acknowledgment.acknowledge();
        return;
      }

      // Process the event
      eventHandler.handle(event);

      // Acknowledge successful processing
      acknowledgment.acknowledge();
      
      log.debug("Successfully processed NotificationRequestedEvent for user: {} and article: {}", 
          event.getTelegramChatId(), event.getArticleTitle());

    } catch (Exception e) {
      log.error("Error processing NotificationRequestedEvent at partition {} offset {}: {}", 
          partition, offset, e.getMessage(), e);
      
      // Don't acknowledge - this will trigger retry or dead letter queue handling
      throw new RuntimeException("Failed to process NotificationRequestedEvent", e);
    }
  }

  /**
   * Deserializes the event payload into a NotificationRequestedEvent object.
   *
   * @param payload the JSON payload
   * @return the deserialized event, or null if deserialization fails
   */
  private NotificationRequestedEvent deserializeEvent(String payload) {
    try {
      return kafkaObjectMapper.readValue(payload, NotificationRequestedEvent.class);
    } catch (Exception e) {
      log.error("Failed to deserialize NotificationRequestedEvent payload: {}", payload, e);
      return null;
    }
  }
}
