package com.phdhuy.bullishbot.infrastructure.kafka.config;

import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;

/**
 * Kafka Topic Configuration for Bullish Bot.
 * 
 * <p>Defines all Kafka topics used by the application with proper partitioning,
 * replication, and retention policies optimized for the news processing workflow.
 */
@Configuration
@Slf4j
public class KafkaTopicConfig {

  @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
  private String bootstrapServers;

  @Value("${spring.kafka.topics.replication-factor:1}")
  private Integer replicationFactor;

  // Topic names as constants for consistency
  public static final String ARTICLES_CRAWLED_TOPIC = "bullish-bot.articles.crawled";
  public static final String TICKERS_EXTRACTED_TOPIC = "bullish-bot.tickers.extracted";
  public static final String NOTIFICATIONS_REQUESTED_TOPIC = "bullish-bot.notifications.requested";
  public static final String NOTIFICATIONS_SENT_TOPIC = "bullish-bot.notifications.sent";
  public static final String NOTIFICATIONS_FAILED_DLQ_TOPIC = "bullish-bot.notifications.failed.dlq";

  /**
   * Creates KafkaAdmin for topic management.
   *
   * @return configured KafkaAdmin
   */
  @Bean
  public KafkaAdmin kafkaAdmin() {
    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
    
    KafkaAdmin admin = new KafkaAdmin(configs);
    admin.setFatalIfBrokerNotAvailable(true);
    
    log.info("KafkaAdmin configured with bootstrap servers: {}", bootstrapServers);
    return admin;
  }

  /**
   * Topic for newly crawled RSS articles.
   * 
   * <p>This topic receives events when new articles are successfully crawled from RSS feeds.
   * Partitioned by RSS feed source for parallel processing.
   *
   * @return NewTopic configuration
   */
  @Bean
  public NewTopic articlesCrawledTopic() {
    return TopicBuilder.name(ARTICLES_CRAWLED_TOPIC)
        .partitions(3)
        .replicas(replicationFactor)
        .config("retention.ms", "604800000") // 7 days
        .config("cleanup.policy", "delete")
        .config("compression.type", "snappy")
        .config("min.insync.replicas", "1")
        .build();
  }

  /**
   * Topic for articles with extracted ticker symbols.
   * 
   * <p>This topic receives events when ticker symbols have been successfully extracted
   * from article content. Used to trigger notification processing.
   *
   * @return NewTopic configuration
   */
  @Bean
  public NewTopic tickersExtractedTopic() {
    return TopicBuilder.name(TICKERS_EXTRACTED_TOPIC)
        .partitions(3)
        .replicas(replicationFactor)
        .config("retention.ms", "604800000") // 7 days
        .config("cleanup.policy", "delete")
        .config("compression.type", "snappy")
        .config("min.insync.replicas", "1")
        .build();
  }

  /**
   * Topic for notification requests.
   * 
   * <p>This topic receives events when notifications need to be sent to users.
   * Higher partition count to handle notification volume.
   *
   * @return NewTopic configuration
   */
  @Bean
  public NewTopic notificationsRequestedTopic() {
    return TopicBuilder.name(NOTIFICATIONS_REQUESTED_TOPIC)
        .partitions(5)
        .replicas(replicationFactor)
        .config("retention.ms", "259200000") // 3 days
        .config("cleanup.policy", "delete")
        .config("compression.type", "snappy")
        .config("min.insync.replicas", "1")
        .build();
  }

  /**
   * Topic for successfully sent notifications.
   * 
   * <p>This topic receives events when notifications are successfully delivered.
   * Used for monitoring and analytics.
   *
   * @return NewTopic configuration
   */
  @Bean
  public NewTopic notificationsSentTopic() {
    return TopicBuilder.name(NOTIFICATIONS_SENT_TOPIC)
        .partitions(2)
        .replicas(replicationFactor)
        .config("retention.ms", "86400000") // 1 day
        .config("cleanup.policy", "delete")
        .config("compression.type", "snappy")
        .config("min.insync.replicas", "1")
        .build();
  }

  /**
   * Dead Letter Queue topic for failed notifications.
   * 
   * <p>This topic receives events when notification processing fails after all retries.
   * Longer retention for manual investigation and recovery.
   *
   * @return NewTopic configuration
   */
  @Bean
  public NewTopic notificationsFailedDlqTopic() {
    return TopicBuilder.name(NOTIFICATIONS_FAILED_DLQ_TOPIC)
        .partitions(2)
        .replicas(replicationFactor)
        .config("retention.ms", "2592000000") // 30 days
        .config("cleanup.policy", "delete")
        .config("compression.type", "snappy")
        .config("min.insync.replicas", "1")
        .build();
  }
}
