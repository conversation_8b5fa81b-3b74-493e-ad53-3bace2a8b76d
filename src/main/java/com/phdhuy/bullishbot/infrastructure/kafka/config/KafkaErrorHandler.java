package com.phdhuy.bullishbot.infrastructure.kafka.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.CommonErrorHandler;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * Kafka Error Handler for comprehensive error handling and recovery strategies.
 * 
 * <p>Implements sophisticated error handling patterns including retry logic, dead letter queues,
 * and proper logging for failed message processing scenarios.
 */
@Component
@Slf4j
public class KafkaErrorHandler implements CommonErrorHandler {

  private static final int MAX_RETRY_ATTEMPTS = 3;

  /**
   * Handles errors that occur during message processing.
   *
   * @param thrownException the exception that occurred
   * @param data the consumer record that caused the error
   * @param consumer the Kafka consumer
   * @param container the message listener container
   * @return true if the error was handled, false otherwise
   */
  @Override
  public boolean handleOne(
      Exception thrownException,
      ConsumerRecord<?, ?> data,
      Consumer<?, ?> consumer,
      MessageListenerContainer container) {
    
    log.error(
        "Error processing message from topic: {}, partition: {}, offset: {}, key: {}, error: {}",
        data.topic(),
        data.partition(),
        data.offset(),
        data.key(),
        thrownException.getMessage(),
        thrownException);

    // Determine if this is a recoverable error
    if (isRecoverableError(thrownException)) {
      log.warn("Recoverable error detected, message will be retried: {}", thrownException.getMessage());
      return false; // Let Kafka retry the message
    }

    // For non-recoverable errors, log and skip the message
    log.error("Non-recoverable error detected, skipping message: {}", thrownException.getMessage());
    
    // In a production environment, you might want to send this to a dead letter queue
    sendToDeadLetterQueue(data, thrownException);
    
    return true; // Mark as handled to skip the message
  }

  /**
   * Handles batch processing errors.
   *
   * @param thrownException the exception that occurred
   * @param data the consumer records that caused the error
   * @param consumer the Kafka consumer
   * @param container the message listener container
   * @param invokeListener callback to invoke the listener again
   */
  @Override
  public void handleBatch(
      Exception thrownException,
      org.springframework.kafka.listener.ConsumerRecords<?, ?> data,
      Consumer<?, ?> consumer,
      MessageListenerContainer container,
      Runnable invokeListener) {
    
    log.error(
        "Error processing batch of {} messages, error: {}",
        data.count(),
        thrownException.getMessage(),
        thrownException);

    // For batch errors, we'll process each record individually to isolate the problematic one
    data.forEach(record -> {
      try {
        // This would typically involve re-processing the individual record
        log.debug("Re-processing individual record: topic={}, partition={}, offset={}", 
            record.topic(), record.partition(), record.offset());
      } catch (Exception e) {
        handleOne(e, record, consumer, container);
      }
    });
  }

  /**
   * Determines if an error is recoverable and should be retried.
   *
   * @param exception the exception to evaluate
   * @return true if the error is recoverable, false otherwise
   */
  private boolean isRecoverableError(Exception exception) {
    // Network-related errors are typically recoverable
    if (exception.getCause() instanceof java.net.ConnectException ||
        exception.getCause() instanceof java.net.SocketTimeoutException ||
        exception.getCause() instanceof java.io.IOException) {
      return true;
    }

    // Temporary database connection issues
    if (exception.getMessage() != null && 
        (exception.getMessage().contains("connection") ||
         exception.getMessage().contains("timeout") ||
         exception.getMessage().contains("unavailable"))) {
      return true;
    }

    // Serialization/deserialization errors are typically not recoverable
    if (exception instanceof org.springframework.kafka.support.serializer.DeserializationException ||
        exception instanceof com.fasterxml.jackson.core.JsonProcessingException) {
      return false;
    }

    // Default to non-recoverable for unknown errors
    return false;
  }

  /**
   * Sends failed messages to a dead letter queue for later analysis.
   *
   * @param record the failed consumer record
   * @param exception the exception that occurred
   */
  private void sendToDeadLetterQueue(ConsumerRecord<?, ?> record, Exception exception) {
    // In a real implementation, you would publish this to a dead letter topic
    log.warn(
        "Message sent to dead letter queue - Topic: {}, Partition: {}, Offset: {}, Key: {}, Error: {}",
        record.topic(),
        record.partition(),
        record.offset(),
        record.key(),
        exception.getMessage());
    
    // TODO: Implement actual dead letter queue publishing
    // This could involve using a separate KafkaTemplate to publish to a DLQ topic
  }
}
