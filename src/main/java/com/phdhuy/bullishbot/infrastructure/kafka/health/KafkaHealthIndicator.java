package com.phdhuy.bullishbot.infrastructure.kafka.health;

import java.time.Duration;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeClusterResult;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Component;

/**
 * Health indicator for Kafka connectivity and cluster status.
 * 
 * <p>This component provides health check capabilities for the Kafka cluster,
 * allowing monitoring systems to detect connectivity issues and cluster problems.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class KafkaHealthIndicator implements HealthIndicator {

  private final KafkaAdmin kafkaAdmin;
  private static final Duration HEALTH_CHECK_TIMEOUT = Duration.ofSeconds(10);

  @Override
  public Health health() {
    try {
      return checkKafkaHealth();
    } catch (Exception e) {
      log.error("Kafka health check failed", e);
      return Health.down()
          .withDetail("error", e.getMessage())
          .withDetail("errorType", e.getClass().getSimpleName())
          .build();
    }
  }

  /**
   * Performs the actual Kafka health check.
   *
   * @return Health status
   */
  private Health checkKafkaHealth() {
    try (AdminClient adminClient = AdminClient.create(kafkaAdmin.getConfigurationProperties())) {
      
      DescribeClusterResult clusterResult = adminClient.describeCluster();
      
      // Check cluster ID (this will throw an exception if Kafka is not reachable)
      String clusterId = clusterResult.clusterId()
          .get(HEALTH_CHECK_TIMEOUT.toSeconds(), TimeUnit.SECONDS);
      
      // Check number of nodes
      int nodeCount = clusterResult.nodes()
          .get(HEALTH_CHECK_TIMEOUT.toSeconds(), TimeUnit.SECONDS)
          .size();
      
      // Check controller
      String controllerId = clusterResult.controller()
          .get(HEALTH_CHECK_TIMEOUT.toSeconds(), TimeUnit.SECONDS)
          .idString();

      log.debug("Kafka health check successful - Cluster ID: {}, Nodes: {}, Controller: {}", 
          clusterId, nodeCount, controllerId);

      return Health.up()
          .withDetail("clusterId", clusterId)
          .withDetail("nodeCount", nodeCount)
          .withDetail("controllerId", controllerId)
          .withDetail("status", "Connected")
          .build();

    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.warn("Kafka health check interrupted", e);
      return Health.down()
          .withDetail("error", "Health check interrupted")
          .withDetail("status", "Interrupted")
          .build();
          
    } catch (ExecutionException e) {
      log.warn("Kafka health check execution failed", e);
      return Health.down()
          .withDetail("error", e.getCause() != null ? e.getCause().getMessage() : e.getMessage())
          .withDetail("status", "Execution Failed")
          .build();
          
    } catch (TimeoutException e) {
      log.warn("Kafka health check timed out after {} seconds", HEALTH_CHECK_TIMEOUT.toSeconds());
      return Health.down()
          .withDetail("error", "Health check timed out")
          .withDetail("timeout", HEALTH_CHECK_TIMEOUT.toString())
          .withDetail("status", "Timeout")
          .build();
    }
  }
}
