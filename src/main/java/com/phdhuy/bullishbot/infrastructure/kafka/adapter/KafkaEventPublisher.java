package com.phdhuy.bullishbot.infrastructure.kafka.adapter;

import com.phdhuy.bullishbot.domain.event.ArticleCrawledEvent;
import com.phdhuy.bullishbot.domain.event.DomainEvent;
import com.phdhuy.bullishbot.domain.event.NotificationFailedEvent;
import com.phdhuy.bullishbot.domain.event.NotificationRequestedEvent;
import com.phdhuy.bullishbot.domain.event.NotificationSentEvent;
import com.phdhuy.bullishbot.domain.event.TickersExtractedEvent;
import com.phdhuy.bullishbot.domain.port.out.EventPublisher;
import com.phdhuy.bullishbot.infrastructure.kafka.config.KafkaTopicConfig;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

/**
 * Kafka implementation of the EventPublisher port.
 * 
 * <p>This adapter handles publishing domain events to appropriate Kafka topics
 * with proper error handling, logging, and monitoring capabilities.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KafkaEventPublisher implements EventPublisher {

  private final KafkaTemplate<String, Object> kafkaTemplate;

  @Override
  public CompletableFuture<Void> publishEvent(DomainEvent event) {
    if (event == null) {
      throw new IllegalArgumentException("Event cannot be null");
    }

    String topic = determineTopicForEvent(event);
    String partitionKey = generatePartitionKey(event);
    
    return publishEvent(topic, event, partitionKey);
  }

  @Override
  public CompletableFuture<Void> publishEvent(String topic, DomainEvent event) {
    if (topic == null || topic.trim().isEmpty()) {
      throw new IllegalArgumentException("Topic cannot be null or empty");
    }
    if (event == null) {
      throw new IllegalArgumentException("Event cannot be null");
    }

    String partitionKey = generatePartitionKey(event);
    return publishEvent(topic, event, partitionKey);
  }

  @Override
  public CompletableFuture<Void> publishEvent(DomainEvent event, String partitionKey) {
    if (event == null) {
      throw new IllegalArgumentException("Event cannot be null");
    }
    if (partitionKey == null || partitionKey.trim().isEmpty()) {
      throw new IllegalArgumentException("Partition key cannot be null or empty");
    }

    String topic = determineTopicForEvent(event);
    return publishEvent(topic, event, partitionKey);
  }

  @Override
  public CompletableFuture<Void> publishEvent(String topic, DomainEvent event, String partitionKey) {
    if (topic == null || topic.trim().isEmpty()) {
      throw new IllegalArgumentException("Topic cannot be null or empty");
    }
    if (event == null) {
      throw new IllegalArgumentException("Event cannot be null");
    }
    if (partitionKey == null || partitionKey.trim().isEmpty()) {
      throw new IllegalArgumentException("Partition key cannot be null or empty");
    }

    log.debug("Publishing event {} to topic {} with partition key {}", 
        event.getEventType(), topic, partitionKey);

    CompletableFuture<SendResult<String, Object>> kafkaFuture = 
        kafkaTemplate.send(topic, partitionKey, event);

    return kafkaFuture
        .thenApply(result -> {
          log.debug("Successfully published event {} to topic {} at partition {} offset {}", 
              event.getEventType(), 
              result.getRecordMetadata().topic(),
              result.getRecordMetadata().partition(),
              result.getRecordMetadata().offset());
          return null;
        })
        .exceptionally(throwable -> {
          log.error("Failed to publish event {} to topic {}: {}", 
              event.getEventType(), topic, throwable.getMessage(), throwable);
          throw new RuntimeException("Failed to publish event", throwable);
        });
  }

  /**
   * Determines the appropriate Kafka topic for a given domain event.
   *
   * @param event the domain event
   * @return the topic name
   */
  private String determineTopicForEvent(DomainEvent event) {
    return switch (event) {
      case ArticleCrawledEvent ignored -> KafkaTopicConfig.ARTICLES_CRAWLED_TOPIC;
      case TickersExtractedEvent ignored -> KafkaTopicConfig.TICKERS_EXTRACTED_TOPIC;
      case NotificationRequestedEvent ignored -> KafkaTopicConfig.NOTIFICATIONS_REQUESTED_TOPIC;
      case NotificationSentEvent ignored -> KafkaTopicConfig.NOTIFICATIONS_SENT_TOPIC;
      case NotificationFailedEvent ignored -> KafkaTopicConfig.NOTIFICATIONS_FAILED_DLQ_TOPIC;
      default -> throw new IllegalArgumentException("Unknown event type: " + event.getClass().getSimpleName());
    };
  }

  /**
   * Generates a partition key for the event to ensure proper ordering.
   *
   * @param event the domain event
   * @return the partition key
   */
  private String generatePartitionKey(DomainEvent event) {
    return switch (event) {
      case ArticleCrawledEvent articleEvent -> 
          articleEvent.getRssFeedId() != null ? articleEvent.getRssFeedId().toString() : "default";
      
      case TickersExtractedEvent tickerEvent -> 
          tickerEvent.getArticleId() != null ? tickerEvent.getArticleId().toString() : "default";
      
      case NotificationRequestedEvent notificationEvent -> 
          notificationEvent.getUserId() != null ? notificationEvent.getUserId().toString() : "default";
      
      case NotificationSentEvent sentEvent -> 
          sentEvent.getUserId() != null ? sentEvent.getUserId().toString() : "default";
      
      case NotificationFailedEvent failedEvent -> 
          failedEvent.getUserId() != null ? failedEvent.getUserId().toString() : "default";
      
      default -> "default";
    };
  }
}
