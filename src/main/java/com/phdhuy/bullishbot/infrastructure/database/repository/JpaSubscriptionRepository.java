package com.phdhuy.bullishbot.infrastructure.database.repository;

import com.phdhuy.bullishbot.domain.entity.Subscription;
import com.phdhuy.bullishbot.domain.entity.Ticker;
import com.phdhuy.bullishbot.domain.entity.User;
import com.phdhuy.bullishbot.domain.port.out.SubscriptionRepository;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JpaSubscriptionRepository
    extends JpaRepository<Subscription, UUID>, SubscriptionRepository {

  @Override
  List<Subscription> findByUser(User user);

  @Override
  Optional<Subscription> findByUserAndTicker(User user, Ticker ticker);

  @Override
  boolean existsByUserAndTicker(User user, Ticker ticker);

  @Override
  @Query("SELECT s FROM Subscription s WHERE s.ticker.symbol IN" + " :tickerSymbols")
  List<Subscription> findByTickerSymbolIn(@Param("tickerSymbols") Set<String> tickerSymbols);

  @Query("SELECT s FROM Subscription s JOIN FETCH s.ticker WHERE s.user = :user")
  List<Subscription> findByUserWithTicker(@Param("user") User user);
}
