package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Domain event published when a notification fails to be sent to a user.
 * 
 * <p>This event is used for error handling, retry mechanisms, and monitoring
 * of failed notification deliveries in the system.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NotificationFailedEvent extends DomainEvent {

  /**
   * Unique identifier of the original notification request.
   */
  @JsonProperty("notificationId")
  private UUID notificationId;

  /**
   * Unique identifier of the user who should have received the notification.
   */
  @JsonProperty("userId")
  private UUID userId;

  /**
   * Telegram chat ID of the user.
   */
  @JsonProperty("telegramChatId")
  private Long telegramChatId;

  /**
   * Unique identifier of the news article.
   */
  @JsonProperty("articleId")
  private UUID articleId;

  /**
   * Title of the article that failed to be notified about.
   */
  @JsonProperty("articleTitle")
  private String articleTitle;

  /**
   * Set of ticker symbols that were supposed to be mentioned in the notification.
   */
  @JsonProperty("matchedTickers")
  private Set<String> matchedTickers;

  /**
   * When the notification failure occurred.
   */
  @JsonProperty("failedAt")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant failedAt;

  /**
   * Channel through which the notification was attempted (e.g., "telegram").
   */
  @JsonProperty("channel")
  private String channel = "telegram";

  /**
   * Error message describing the failure.
   */
  @JsonProperty("errorMessage")
  private String errorMessage;

  /**
   * Error code or type for categorizing the failure.
   */
  @JsonProperty("errorType")
  private String errorType;

  /**
   * Number of retry attempts made before this failure.
   */
  @JsonProperty("retryCount")
  private Integer retryCount = 0;

  /**
   * Whether this failure is considered recoverable and should be retried.
   */
  @JsonProperty("recoverable")
  private Boolean recoverable = true;

  /**
   * Stack trace of the error (truncated for event size).
   */
  @JsonProperty("stackTrace")
  private String stackTrace;

  /**
   * Creates a NotificationFailedEvent for a failed notification.
   *
   * @param notificationId unique identifier of the notification
   * @param userId unique identifier of the user
   * @param telegramChatId Telegram chat ID of the user
   * @param articleId unique identifier of the article
   * @param articleTitle title of the article
   * @param matchedTickers tickers that should have been mentioned
   * @param failedAt when the failure occurred
   * @param errorMessage description of the error
   * @param errorType type/category of the error
   * @param retryCount number of retry attempts made
   * @param recoverable whether the error is recoverable
   * @return new NotificationFailedEvent instance
   */
  public static NotificationFailedEvent of(
      UUID notificationId,
      UUID userId,
      Long telegramChatId,
      UUID articleId,
      String articleTitle,
      Set<String> matchedTickers,
      Instant failedAt,
      String errorMessage,
      String errorType,
      Integer retryCount,
      Boolean recoverable) {
    
    NotificationFailedEvent event = new NotificationFailedEvent();
    event.setNotificationId(notificationId);
    event.setUserId(userId);
    event.setTelegramChatId(telegramChatId);
    event.setArticleId(articleId);
    event.setArticleTitle(articleTitle);
    event.setMatchedTickers(matchedTickers);
    event.setFailedAt(failedAt);
    event.setChannel("telegram");
    event.setErrorMessage(errorMessage);
    event.setErrorType(errorType);
    event.setRetryCount(retryCount);
    event.setRecoverable(recoverable);
    
    return event;
  }

  /**
   * Creates a NotificationFailedEvent from an exception.
   *
   * @param notificationId unique identifier of the notification
   * @param userId unique identifier of the user
   * @param telegramChatId Telegram chat ID of the user
   * @param articleId unique identifier of the article
   * @param articleTitle title of the article
   * @param matchedTickers tickers that should have been mentioned
   * @param exception the exception that caused the failure
   * @param retryCount number of retry attempts made
   * @return new NotificationFailedEvent instance
   */
  public static NotificationFailedEvent fromException(
      UUID notificationId,
      UUID userId,
      Long telegramChatId,
      UUID articleId,
      String articleTitle,
      Set<String> matchedTickers,
      Exception exception,
      Integer retryCount) {
    
    String errorType = exception.getClass().getSimpleName();
    String errorMessage = exception.getMessage() != null ? exception.getMessage() : errorType;
    boolean recoverable = isRecoverableError(exception);
    
    NotificationFailedEvent event = of(
        notificationId, userId, telegramChatId, articleId, articleTitle,
        matchedTickers, Instant.now(), errorMessage, errorType, retryCount, recoverable);
    
    // Add truncated stack trace for debugging
    event.setStackTrace(truncateStackTrace(exception));
    
    return event;
  }

  /**
   * Gets a formatted string of matched tickers for logging.
   *
   * @return comma-separated list of matched tickers
   */
  public String getMatchedTickersString() {
    return matchedTickers != null ? String.join(", ", matchedTickers) : "";
  }

  /**
   * Determines if an error is recoverable based on the exception type.
   *
   * @param exception the exception to evaluate
   * @return true if recoverable, false otherwise
   */
  private static boolean isRecoverableError(Exception exception) {
    // Network-related errors are typically recoverable
    return exception.getCause() instanceof java.net.ConnectException ||
           exception.getCause() instanceof java.net.SocketTimeoutException ||
           exception.getCause() instanceof java.io.IOException ||
           (exception.getMessage() != null && 
            (exception.getMessage().contains("timeout") ||
             exception.getMessage().contains("connection") ||
             exception.getMessage().contains("unavailable")));
  }

  /**
   * Truncates stack trace to prevent oversized events.
   *
   * @param exception the exception
   * @return truncated stack trace
   */
  private static String truncateStackTrace(Exception exception) {
    if (exception == null) {
      return null;
    }
    
    String stackTrace = java.util.Arrays.toString(exception.getStackTrace());
    final int maxLength = 1000;
    
    if (stackTrace.length() <= maxLength) {
      return stackTrace;
    }
    
    return stackTrace.substring(0, maxLength) + "... [truncated]";
  }
}
