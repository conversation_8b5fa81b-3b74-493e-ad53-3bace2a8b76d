package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Base class for all domain events in the Bullish Bot system.
 * 
 * <p>Provides common properties and behavior for domain events following DDD principles.
 * All domain events should extend this class to ensure consistency and proper event tracking.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class DomainEvent {

  /**
   * Unique identifier for this event instance.
   */
  @JsonProperty("eventId")
  private UUID eventId = UUID.randomUUID();

  /**
   * Timestamp when the event occurred.
   */
  @JsonProperty("occurredAt")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant occurredAt = Instant.now();

  /**
   * Version of the event schema for backward compatibility.
   */
  @JsonProperty("eventVersion")
  private String eventVersion = "1.0";

  /**
   * Source service or component that generated this event.
   */
  @JsonProperty("source")
  private String source = "bullish-bot";

  /**
   * Type of the event (automatically derived from class name).
   */
  @JsonProperty("eventType")
  public String getEventType() {
    return this.getClass().getSimpleName();
  }

  /**
   * Creates a new domain event with current timestamp.
   *
   * @param source the source component generating the event
   */
  protected DomainEvent(String source) {
    this.eventId = UUID.randomUUID();
    this.occurredAt = Instant.now();
    this.eventVersion = "1.0";
    this.source = source;
  }
}
