package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Domain event published when a notification is successfully sent to a user.
 * 
 * <p>This event is used for monitoring, analytics, and audit trails of successful
 * notification deliveries in the system.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSentEvent extends DomainEvent {

  /**
   * Unique identifier of the original notification request.
   */
  @JsonProperty("notificationId")
  private UUID notificationId;

  /**
   * Unique identifier of the user who received the notification.
   */
  @JsonProperty("userId")
  private UUID userId;

  /**
   * Telegram chat ID of the user.
   */
  @JsonProperty("telegramChatId")
  private Long telegramChatId;

  /**
   * Unique identifier of the news article.
   */
  @JsonProperty("articleId")
  private UUID articleId;

  /**
   * Title of the article that was notified about.
   */
  @JsonProperty("articleTitle")
  private String articleTitle;

  /**
   * Set of ticker symbols that were mentioned in the notification.
   */
  @JsonProperty("matchedTickers")
  private Set<String> matchedTickers;

  /**
   * When the notification was actually sent.
   */
  @JsonProperty("sentAt")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant sentAt;

  /**
   * Channel through which the notification was sent (e.g., "telegram").
   */
  @JsonProperty("channel")
  private String channel = "telegram";

  /**
   * Status of the notification delivery.
   */
  @JsonProperty("deliveryStatus")
  private String deliveryStatus = "SUCCESS";

  /**
   * Time taken to deliver the notification in milliseconds.
   */
  @JsonProperty("deliveryTimeMs")
  private Long deliveryTimeMs;

  /**
   * Creates a NotificationSentEvent for a successfully sent notification.
   *
   * @param notificationId unique identifier of the notification
   * @param userId unique identifier of the user
   * @param telegramChatId Telegram chat ID of the user
   * @param articleId unique identifier of the article
   * @param articleTitle title of the article
   * @param matchedTickers tickers mentioned in the notification
   * @param sentAt when the notification was sent
   * @param deliveryTimeMs time taken to deliver in milliseconds
   * @return new NotificationSentEvent instance
   */
  public static NotificationSentEvent of(
      UUID notificationId,
      UUID userId,
      Long telegramChatId,
      UUID articleId,
      String articleTitle,
      Set<String> matchedTickers,
      Instant sentAt,
      Long deliveryTimeMs) {
    
    NotificationSentEvent event = new NotificationSentEvent();
    event.setNotificationId(notificationId);
    event.setUserId(userId);
    event.setTelegramChatId(telegramChatId);
    event.setArticleId(articleId);
    event.setArticleTitle(articleTitle);
    event.setMatchedTickers(matchedTickers);
    event.setSentAt(sentAt);
    event.setChannel("telegram");
    event.setDeliveryStatus("SUCCESS");
    event.setDeliveryTimeMs(deliveryTimeMs);
    
    return event;
  }

  /**
   * Gets a formatted string of matched tickers for logging.
   *
   * @return comma-separated list of matched tickers
   */
  public String getMatchedTickersString() {
    return matchedTickers != null ? String.join(", ", matchedTickers) : "";
  }
}
