package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Domain event published when a notification needs to be sent to a user.
 * 
 * <p>This event triggers the notification delivery pipeline to send the actual
 * notification via the configured channels (e.g., Telegram).
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRequestedEvent extends DomainEvent {

  /**
   * Unique identifier for this notification request.
   */
  @JsonProperty("notificationId")
  private UUID notificationId;

  /**
   * Unique identifier of the user to notify.
   */
  @JsonProperty("userId")
  private UUID userId;

  /**
   * Telegram chat ID of the user.
   */
  @JsonProperty("telegramChatId")
  private Long telegramChatId;

  /**
   * Username of the user (for logging/debugging).
   */
  @JsonProperty("username")
  private String username;

  /**
   * Unique identifier of the news article.
   */
  @JsonProperty("articleId")
  private UUID articleId;

  /**
   * Title of the article.
   */
  @JsonProperty("articleTitle")
  private String articleTitle;

  /**
   * URL of the original article.
   */
  @JsonProperty("articleUrl")
  private String articleUrl;

  /**
   * Description/summary of the article.
   */
  @JsonProperty("articleDescription")
  private String articleDescription;

  /**
   * When the article was originally published.
   */
  @JsonProperty("publishedDate")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant publishedDate;

  /**
   * Set of ticker symbols that matched the user's subscriptions.
   */
  @JsonProperty("matchedTickers")
  private Set<String> matchedTickers;

  /**
   * Name of the RSS feed source.
   */
  @JsonProperty("rssFeedName")
  private String rssFeedName;

  /**
   * Priority level for the notification (for future use).
   */
  @JsonProperty("priority")
  private String priority = "NORMAL";

  /**
   * Creates a NotificationRequestedEvent for a user and article.
   *
   * @param notificationId unique identifier for the notification
   * @param userId unique identifier of the user
   * @param telegramChatId Telegram chat ID of the user
   * @param username username of the user
   * @param articleId unique identifier of the article
   * @param articleTitle title of the article
   * @param articleUrl URL of the article
   * @param articleDescription description of the article
   * @param publishedDate when the article was published
   * @param matchedTickers tickers that matched user subscriptions
   * @param rssFeedName name of the RSS feed source
   * @return new NotificationRequestedEvent instance
   */
  public static NotificationRequestedEvent of(
      UUID notificationId,
      UUID userId,
      Long telegramChatId,
      String username,
      UUID articleId,
      String articleTitle,
      String articleUrl,
      String articleDescription,
      Instant publishedDate,
      Set<String> matchedTickers,
      String rssFeedName) {
    
    NotificationRequestedEvent event = new NotificationRequestedEvent();
    event.setNotificationId(notificationId);
    event.setUserId(userId);
    event.setTelegramChatId(telegramChatId);
    event.setUsername(username);
    event.setArticleId(articleId);
    event.setArticleTitle(articleTitle);
    event.setArticleUrl(articleUrl);
    event.setArticleDescription(truncateDescription(articleDescription));
    event.setPublishedDate(publishedDate);
    event.setMatchedTickers(matchedTickers);
    event.setRssFeedName(rssFeedName);
    event.setPriority("NORMAL");
    
    return event;
  }

  /**
   * Gets a formatted string of matched tickers for display.
   *
   * @return comma-separated list of matched tickers
   */
  public String getMatchedTickersString() {
    return matchedTickers != null ? String.join(", ", matchedTickers) : "";
  }

  /**
   * Checks if this notification has matched tickers.
   *
   * @return true if there are matched tickers, false otherwise
   */
  public boolean hasMatchedTickers() {
    return matchedTickers != null && !matchedTickers.isEmpty();
  }

  /**
   * Truncates description to prevent oversized events.
   *
   * @param description the original description
   * @return truncated description
   */
  private static String truncateDescription(String description) {
    if (description == null) {
      return null;
    }
    
    final int maxLength = 500; // Limit description size in events
    if (description.length() <= maxLength) {
      return description;
    }
    
    return description.substring(0, maxLength) + "...";
  }
}
