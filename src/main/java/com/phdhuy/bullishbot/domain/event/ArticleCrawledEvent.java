package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Domain event published when a new RSS article is successfully crawled and saved.
 * 
 * <p>This event triggers the content processing pipeline to extract ticker symbols
 * and prepare the article for notification processing.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ArticleCrawledEvent extends DomainEvent {

  /**
   * Unique identifier of the crawled news article.
   */
  @JsonProperty("articleId")
  private UUID articleId;

  /**
   * Title of the crawled article.
   */
  @JsonProperty("title")
  private String title;

  /**
   * URL of the original article.
   */
  @JsonProperty("url")
  private String url;

  /**
   * Description/summary of the article.
   */
  @JsonProperty("description")
  private String description;

  /**
   * Content of the article (may be truncated for event size).
   */
  @JsonProperty("content")
  private String content;

  /**
   * Author of the article.
   */
  @JsonProperty("author")
  private String author;

  /**
   * When the article was originally published.
   */
  @JsonProperty("publishedDate")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant publishedDate;

  /**
   * Unique identifier of the RSS feed source.
   */
  @JsonProperty("rssFeedId")
  private UUID rssFeedId;

  /**
   * Name of the RSS feed source.
   */
  @JsonProperty("rssFeedName")
  private String rssFeedName;

  /**
   * URL of the RSS feed source.
   */
  @JsonProperty("rssFeedUrl")
  private String rssFeedUrl;

  /**
   * Creates an ArticleCrawledEvent from article and feed information.
   *
   * @param articleId unique identifier of the article
   * @param title title of the article
   * @param url URL of the article
   * @param description description of the article
   * @param content content of the article
   * @param author author of the article
   * @param publishedDate when the article was published
   * @param rssFeedId unique identifier of the RSS feed
   * @param rssFeedName name of the RSS feed
   * @param rssFeedUrl URL of the RSS feed
   * @return new ArticleCrawledEvent instance
   */
  public static ArticleCrawledEvent of(
      UUID articleId,
      String title,
      String url,
      String description,
      String content,
      String author,
      Instant publishedDate,
      UUID rssFeedId,
      String rssFeedName,
      String rssFeedUrl) {
    
    ArticleCrawledEvent event = new ArticleCrawledEvent();
    event.setArticleId(articleId);
    event.setTitle(title);
    event.setUrl(url);
    event.setDescription(description);
    event.setContent(truncateContent(content));
    event.setAuthor(author);
    event.setPublishedDate(publishedDate);
    event.setRssFeedId(rssFeedId);
    event.setRssFeedName(rssFeedName);
    event.setRssFeedUrl(rssFeedUrl);
    
    return event;
  }

  /**
   * Truncates content to prevent oversized events.
   *
   * @param content the original content
   * @return truncated content
   */
  private static String truncateContent(String content) {
    if (content == null) {
      return null;
    }
    
    final int maxLength = 2000; // Limit content size in events
    if (content.length() <= maxLength) {
      return content;
    }
    
    return content.substring(0, maxLength) + "... [truncated]";
  }
}
