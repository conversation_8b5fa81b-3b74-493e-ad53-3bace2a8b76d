package com.phdhuy.bullishbot.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Domain event published when ticker symbols are successfully extracted from an article.
 * 
 * <p>This event triggers the notification processing pipeline to find subscribed users
 * and send relevant notifications about the tickers mentioned in the article.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class TickersExtractedEvent extends DomainEvent {

  /**
   * Unique identifier of the news article.
   */
  @JsonProperty("articleId")
  private UUID articleId;

  /**
   * Title of the article for context.
   */
  @JsonProperty("title")
  private String title;

  /**
   * URL of the original article.
   */
  @JsonProperty("url")
  private String url;

  /**
   * Description/summary of the article.
   */
  @JsonProperty("description")
  private String description;

  /**
   * When the article was originally published.
   */
  @JsonProperty("publishedDate")
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
  private Instant publishedDate;

  /**
   * Set of ticker symbols extracted from the article content.
   */
  @JsonProperty("extractedTickers")
  private Set<String> extractedTickers;

  /**
   * Unique identifier of the RSS feed source.
   */
  @JsonProperty("rssFeedId")
  private UUID rssFeedId;

  /**
   * Name of the RSS feed source.
   */
  @JsonProperty("rssFeedName")
  private String rssFeedName;

  /**
   * Creates a TickersExtractedEvent from article and ticker information.
   *
   * @param articleId unique identifier of the article
   * @param title title of the article
   * @param url URL of the article
   * @param description description of the article
   * @param publishedDate when the article was published
   * @param extractedTickers set of extracted ticker symbols
   * @param rssFeedId unique identifier of the RSS feed
   * @param rssFeedName name of the RSS feed
   * @return new TickersExtractedEvent instance
   */
  public static TickersExtractedEvent of(
      UUID articleId,
      String title,
      String url,
      String description,
      Instant publishedDate,
      Set<String> extractedTickers,
      UUID rssFeedId,
      String rssFeedName) {
    
    TickersExtractedEvent event = new TickersExtractedEvent();
    event.setArticleId(articleId);
    event.setTitle(title);
    event.setUrl(url);
    event.setDescription(description);
    event.setPublishedDate(publishedDate);
    event.setExtractedTickers(extractedTickers);
    event.setRssFeedId(rssFeedId);
    event.setRssFeedName(rssFeedName);
    
    return event;
  }

  /**
   * Checks if any tickers were extracted from the article.
   *
   * @return true if tickers were found, false otherwise
   */
  public boolean hasExtractedTickers() {
    return extractedTickers != null && !extractedTickers.isEmpty();
  }

  /**
   * Gets the number of unique tickers extracted.
   *
   * @return count of extracted tickers
   */
  public int getTickerCount() {
    return extractedTickers != null ? extractedTickers.size() : 0;
  }
}
