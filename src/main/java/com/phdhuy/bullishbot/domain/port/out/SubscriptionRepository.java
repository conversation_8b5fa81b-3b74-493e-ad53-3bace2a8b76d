package com.phdhuy.bullishbot.domain.port.out;

import com.phdhuy.bullishbot.domain.entity.Subscription;
import com.phdhuy.bullishbot.domain.entity.Ticker;
import com.phdhuy.bullishbot.domain.entity.User;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface SubscriptionRepository {
  List<Subscription> findByUser(User user);

  Optional<Subscription> findByUserAndTicker(User user, Ticker ticker);

  Subscription save(Subscription subscription);

  void delete(Subscription subscription);

  boolean existsByUserAndTicker(User user, Ticker ticker);

  List<Subscription> findByTickerSymbolIn(Set<String> tickerSymbols);

  List<Subscription> findByUserWithTicker(User user);
}
