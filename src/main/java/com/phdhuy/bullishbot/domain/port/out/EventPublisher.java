package com.phdhuy.bullishbot.domain.port.out;

import com.phdhuy.bullishbot.domain.event.DomainEvent;
import java.util.concurrent.CompletableFuture;

/**
 * Output port for publishing domain events to the message broker.
 * 
 * <p>This interface defines the contract for publishing domain events in an asynchronous,
 * non-blocking manner. Implementations should handle serialization, routing, and error
 * handling according to the specific message broker requirements.
 */
public interface EventPublisher {

  /**
   * Publishes a domain event to the appropriate topic.
   * 
   * <p>The topic is determined based on the event type. Events are published
   * asynchronously and the returned CompletableFuture can be used to handle
   * success or failure scenarios.
   *
   * @param event the domain event to publish
   * @return CompletableFuture that completes when the event is published
   * @throws IllegalArgumentException if the event is null or invalid
   */
  CompletableFuture<Void> publishEvent(DomainEvent event);

  /**
   * Publishes a domain event to a specific topic.
   * 
   * <p>This method allows explicit topic specification, useful for routing
   * events to specific topics or for testing purposes.
   *
   * @param topic the target topic name
   * @param event the domain event to publish
   * @return CompletableFuture that completes when the event is published
   * @throws IllegalArgumentException if the topic or event is null/invalid
   */
  CompletableFuture<Void> publishEvent(String topic, DomainEvent event);

  /**
   * Publishes a domain event with a specific partition key.
   * 
   * <p>The partition key is used to ensure related events are processed
   * in order by routing them to the same partition.
   *
   * @param event the domain event to publish
   * @param partitionKey the key used for partitioning
   * @return CompletableFuture that completes when the event is published
   * @throws IllegalArgumentException if the event or partition key is null/invalid
   */
  CompletableFuture<Void> publishEvent(DomainEvent event, String partitionKey);

  /**
   * Publishes a domain event to a specific topic with a partition key.
   * 
   * <p>This method provides full control over topic routing and partitioning.
   *
   * @param topic the target topic name
   * @param event the domain event to publish
   * @param partitionKey the key used for partitioning
   * @return CompletableFuture that completes when the event is published
   * @throws IllegalArgumentException if any parameter is null/invalid
   */
  CompletableFuture<Void> publishEvent(String topic, DomainEvent event, String partitionKey);
}
