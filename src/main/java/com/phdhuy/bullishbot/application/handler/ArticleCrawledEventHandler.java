package com.phdhuy.bullishbot.application.handler;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.event.ArticleCrawledEvent;
import com.phdhuy.bullishbot.domain.port.in.ProcessNewsContentUseCase;
import com.phdhuy.bullishbot.domain.port.out.NewsArticleRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Event handler for ArticleCrawledEvent.
 * 
 * <p>This handler processes newly crawled articles by extracting ticker symbols
 * from their content and updating the article with the extracted information.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ArticleCrawledEventHandler {

  private final ProcessNewsContentUseCase processNewsContentUseCase;
  private final NewsArticleRepository newsArticleRepository;

  /**
   * Handles the ArticleCrawledEvent by processing the article content.
   *
   * @param event the ArticleCrawledEvent to handle
   */
  @Transactional
  public void handle(ArticleCrawledEvent event) {
    if (event == null) {
      log.warn("Received null ArticleCrawledEvent, skipping processing");
      return;
    }

    log.info("Processing ArticleCrawledEvent for article: {} (ID: {})", 
        event.getTitle(), event.getArticleId());

    try {
      // Retrieve the full article from the database
      Optional<NewsArticle> articleOpt = newsArticleRepository.findById(event.getArticleId());
      
      if (articleOpt.isEmpty()) {
        log.warn("Article with ID {} not found in database, skipping content processing", 
            event.getArticleId());
        return;
      }

      NewsArticle article = articleOpt.get();
      
      // Process the article content to extract ticker symbols
      processNewsContentUseCase.processNewsContent(article);
      
      log.info("Successfully processed content for article: {} (ID: {})", 
          event.getTitle(), event.getArticleId());

    } catch (Exception e) {
      log.error("Error processing ArticleCrawledEvent for article: {} (ID: {})", 
          event.getTitle(), event.getArticleId(), e);
      throw e; // Re-throw to trigger error handling in Kafka consumer
    }
  }

  /**
   * Validates the ArticleCrawledEvent before processing.
   *
   * @param event the event to validate
   * @return true if valid, false otherwise
   */
  private boolean isValidEvent(ArticleCrawledEvent event) {
    if (event.getArticleId() == null) {
      log.warn("ArticleCrawledEvent has null articleId, skipping");
      return false;
    }

    if (event.getTitle() == null || event.getTitle().trim().isEmpty()) {
      log.warn("ArticleCrawledEvent has null or empty title, skipping");
      return false;
    }

    return true;
  }
}
