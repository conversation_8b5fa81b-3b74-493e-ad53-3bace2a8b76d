package com.phdhuy.bullishbot.application.handler;

import com.phdhuy.bullishbot.domain.entity.NewsNotification;
import com.phdhuy.bullishbot.domain.entity.enums.NotificationStatus;
import com.phdhuy.bullishbot.domain.event.NotificationRequestedEvent;
import com.phdhuy.bullishbot.domain.port.out.NewsNotificationRepository;
import com.phdhuy.bullishbot.domain.port.out.TelegramService;
import com.phdhuy.bullishbot.infrastructure.kafka.service.EventPublishingService;
import java.time.Instant;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Event handler for NotificationRequestedEvent.
 * 
 * <p>This handler processes notification requests by sending the actual notification
 * to the user via the configured channels (e.g., Telegram) and updating the notification status.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NotificationRequestedEventHandler {

  private final TelegramService telegramService;
  private final NewsNotificationRepository newsNotificationRepository;
  private final EventPublishingService eventPublishingService;

  /**
   * Handles the NotificationRequestedEvent by sending the notification to the user.
   *
   * @param event the NotificationRequestedEvent to handle
   */
  @Transactional
  public void handle(NotificationRequestedEvent event) {
    if (event == null) {
      log.warn("Received null NotificationRequestedEvent, skipping processing");
      return;
    }

    if (!isValidEvent(event)) {
      return;
    }

    log.info("Processing NotificationRequestedEvent for user: {} and article: {} (ID: {})", 
        event.getTelegramChatId(), event.getArticleTitle(), event.getArticleId());

    long startTime = System.currentTimeMillis();

    try {
      // Find the notification record in the database
      Optional<NewsNotification> notificationOpt = 
          newsNotificationRepository.findById(event.getNotificationId());
      
      if (notificationOpt.isEmpty()) {
        log.warn("Notification with ID {} not found in database, creating new record", 
            event.getNotificationId());
        // In this case, we'll still send the notification but won't update the database record
      }

      // Format and send the notification message
      String message = formatNotificationMessage(event);
      telegramService.sendMessage(event.getTelegramChatId(), message);

      long deliveryTime = System.currentTimeMillis() - startTime;

      // Update notification status if record exists
      if (notificationOpt.isPresent()) {
        NewsNotification notification = notificationOpt.get();
        notification.setStatus(NotificationStatus.SENT);
        notification.setSentAt(Instant.now());
        newsNotificationRepository.save(notification);

        // Publish success event
        eventPublishingService.publishNotificationSentEvent(
            notification, event.getMatchedTickers(), deliveryTime);
      }

      log.info("Successfully sent notification to user: {} for article: {} (delivery time: {}ms)", 
          event.getTelegramChatId(), event.getArticleTitle(), deliveryTime);

    } catch (Exception e) {
      log.error("Error processing NotificationRequestedEvent for user: {} and article: {}", 
          event.getTelegramChatId(), event.getArticleTitle(), e);

      // Update notification status to failed if record exists
      Optional<NewsNotification> notificationOpt = 
          newsNotificationRepository.findById(event.getNotificationId());
      
      if (notificationOpt.isPresent()) {
        NewsNotification notification = notificationOpt.get();
        notification.setStatus(NotificationStatus.FAILED);
        notification.setErrorMessage(e.getMessage());
        notification.setRetryCount(notification.getRetryCount() + 1);
        newsNotificationRepository.save(notification);

        // Publish failure event
        eventPublishingService.publishNotificationFailedEvent(
            notification, event.getMatchedTickers(), e);
      } else {
        // Publish failure event without notification record
        eventPublishingService.publishNotificationFailedEvent(
            event.getNotificationId(), event.getUserId(), event.getTelegramChatId(),
            event.getArticleId(), event.getArticleTitle(), event.getMatchedTickers(),
            e.getMessage(), e.getClass().getSimpleName(), 0, true);
      }

      throw e; // Re-throw to trigger error handling in Kafka consumer
    }
  }

  /**
   * Formats the notification message for the user.
   *
   * @param event the notification event
   * @return formatted message
   */
  private String formatNotificationMessage(NotificationRequestedEvent event) {
    StringBuilder message = new StringBuilder();
    
    message.append("🚀 *News Alert*\n\n");
    message.append("📰 *").append(escapeMarkdown(event.getArticleTitle())).append("*\n\n");
    
    if (event.getArticleDescription() != null && !event.getArticleDescription().trim().isEmpty()) {
      message.append("📝 ").append(escapeMarkdown(event.getArticleDescription())).append("\n\n");
    }
    
    message.append("🏷️ *Tickers:* ").append(escapeMarkdown(event.getMatchedTickersString())).append("\n");
    message.append("📡 *Source:* ").append(escapeMarkdown(event.getRssFeedName())).append("\n");
    message.append("🔗 [Read More](").append(event.getArticleUrl()).append(")");
    
    return message.toString();
  }

  /**
   * Escapes special characters for Telegram MarkdownV2 format.
   *
   * @param text the text to escape
   * @return escaped text
   */
  private String escapeMarkdown(String text) {
    if (text == null) {
      return "";
    }
    
    return text.replaceAll("([_*\\[\\]()~`>#+\\-=|{}.!])", "\\\\$1");
  }

  /**
   * Validates the NotificationRequestedEvent before processing.
   *
   * @param event the event to validate
   * @return true if valid, false otherwise
   */
  private boolean isValidEvent(NotificationRequestedEvent event) {
    if (event.getNotificationId() == null) {
      log.warn("NotificationRequestedEvent has null notificationId, skipping");
      return false;
    }

    if (event.getTelegramChatId() == null) {
      log.warn("NotificationRequestedEvent has null telegramChatId, skipping");
      return false;
    }

    if (event.getArticleId() == null) {
      log.warn("NotificationRequestedEvent has null articleId, skipping");
      return false;
    }

    if (!event.hasMatchedTickers()) {
      log.warn("NotificationRequestedEvent has no matched tickers, skipping");
      return false;
    }

    return true;
  }
}
