package com.phdhuy.bullishbot.application.handler;

import com.phdhuy.bullishbot.domain.entity.NewsArticle;
import com.phdhuy.bullishbot.domain.event.TickersExtractedEvent;
import com.phdhuy.bullishbot.domain.port.in.SendNotificationUseCase;
import com.phdhuy.bullishbot.domain.port.out.NewsArticleRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Event handler for TickersExtractedEvent.
 * 
 * <p>This handler processes articles with extracted ticker symbols by finding
 * subscribed users and triggering notification processing.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TickersExtractedEventHandler {

  private final SendNotificationUseCase sendNotificationUseCase;
  private final NewsArticleRepository newsArticleRepository;

  /**
   * Handles the TickersExtractedEvent by processing notifications for subscribed users.
   *
   * @param event the TickersExtractedEvent to handle
   */
  @Transactional
  public void handle(TickersExtractedEvent event) {
    if (event == null) {
      log.warn("Received null TickersExtractedEvent, skipping processing");
      return;
    }

    if (!isValidEvent(event)) {
      return;
    }

    log.info("Processing TickersExtractedEvent for article: {} (ID: {}) with tickers: {}", 
        event.getTitle(), event.getArticleId(), event.getExtractedTickers());

    try {
      // Retrieve the full article from the database
      Optional<NewsArticle> articleOpt = newsArticleRepository.findById(event.getArticleId());
      
      if (articleOpt.isEmpty()) {
        log.warn("Article with ID {} not found in database, skipping notification processing", 
            event.getArticleId());
        return;
      }

      NewsArticle article = articleOpt.get();
      
      // Verify that the article has the expected extracted tickers
      if (article.getExtractedTickers() == null || article.getExtractedTickers().isEmpty()) {
        log.warn("Article {} has no extracted tickers in database, skipping notification processing", 
            event.getArticleId());
        return;
      }

      // Process notifications for users subscribed to the extracted tickers
      sendNotificationUseCase.processNewsForNotifications(article);
      
      log.info("Successfully processed notifications for article: {} (ID: {}) with {} tickers", 
          event.getTitle(), event.getArticleId(), event.getTickerCount());

    } catch (Exception e) {
      log.error("Error processing TickersExtractedEvent for article: {} (ID: {})", 
          event.getTitle(), event.getArticleId(), e);
      throw e; // Re-throw to trigger error handling in Kafka consumer
    }
  }

  /**
   * Validates the TickersExtractedEvent before processing.
   *
   * @param event the event to validate
   * @return true if valid, false otherwise
   */
  private boolean isValidEvent(TickersExtractedEvent event) {
    if (event.getArticleId() == null) {
      log.warn("TickersExtractedEvent has null articleId, skipping");
      return false;
    }

    if (event.getTitle() == null || event.getTitle().trim().isEmpty()) {
      log.warn("TickersExtractedEvent has null or empty title, skipping");
      return false;
    }

    if (!event.hasExtractedTickers()) {
      log.warn("TickersExtractedEvent has no extracted tickers, skipping");
      return false;
    }

    return true;
  }
}
